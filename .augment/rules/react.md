---
type: "always_apply"
description: "Trigger whenever you are implementing anything in react."
---

- Use bun instead of npm. It is a 1-1 drop in replacement.
- Always use tailwindcss for styling.
- The react is javascript only and not typescript.
- Never use npm. always bun.
- bun install <package>
- bun run dev
- Always implement within the web folder.
- Do not overcomplicate the project structure
- Use the library dndkit to implement the drag and drop functionalities. ALWAYS USE CONTEXT7 FOR ANY COMPONENT YOU WANT TO USE FROM DNDKIT. ALWAYS.
- ALWAYS use the tool context7 to get uptodate and accurate documentation for any library (dndkit, tailwindcss v4, etc..). ALWAYS. YOU ARE NOT ALLOWED TO IGNORE THIS RULE.
- always reason what is the best way to implement something from a standpoint of a user experience. 
- heavily use modals and drawers whenever needed/ possible.
- keep the styling uniform and modern and animated.
- always add spinner or loading animations whenever needed. 