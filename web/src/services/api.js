import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for loading states
api.interceptors.request.use(
  (config) => {
    // You can add loading state management here
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 404) {
      console.error('Resource not found:', error.response.data);
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data);
    }
    return Promise.reject(error);
  }
);

// Projects API
export const projectsApi = {
  getAll: (params = {}) => api.get('/projects/', { params }),
  getById: (id) => api.get(`/projects/${id}/`),
  getWithEpics: (id) => api.get(`/projects/${id}/with-epics/`),
  create: (data) => api.post('/projects/', data),
  update: (id, data) => api.put(`/projects/${id}/`, data),
  delete: (id) => api.delete(`/projects/${id}/`),
};

// Epics API
export const epicsApi = {
  getAll: (params = {}) => api.get('/epics/', { params }),
  getById: (id) => api.get(`/epics/${id}/`),
  getWithFeatures: (id) => api.get(`/epics/${id}/with-features/`),
  getByProject: (projectId) => api.get('/epics/', { params: { project_id: projectId } }),
  create: (data) => api.post('/epics/', data),
  update: (id, data) => api.put(`/epics/${id}/`, data),
  delete: (id) => api.delete(`/epics/${id}/`),
};

// Features API
export const featuresApi = {
  getAll: (params = {}) => api.get('/features/', { params }),
  getById: (id) => api.get(`/features/${id}/`),
  getWithBacklogItems: (id) => api.get(`/features/${id}/with-backlog-items/`),
  getByEpic: (epicId) => api.get('/features/', { params: { epic_id: epicId } }),
  create: (data) => api.post('/features/', data),
  update: (id, data) => api.put(`/features/${id}/`, data),
  delete: (id) => api.delete(`/features/${id}/`),
};

// Backlog Items API
export const backlogItemsApi = {
  getAll: (params = {}) => api.get('/backlog-items/', { params }),
  getById: (id) => api.get(`/backlog-items/${id}/`),
  getWithTasks: (id) => api.get(`/backlog-items/${id}/with-tasks/`),
  getByFeature: (featureId) => api.get('/backlog-items/', { params: { feature_id: featureId } }),
  create: (data) => api.post('/backlog-items/', data),
  update: (id, data) => api.put(`/backlog-items/${id}/`, data),
  delete: (id) => api.delete(`/backlog-items/${id}/`),
};

// Tasks API
export const tasksApi = {
  getAll: (params = {}) => api.get('/tasks/', { params }),
  getById: (id) => api.get(`/tasks/${id}/`),
  getByBacklogItem: (backlogItemId) => api.get('/tasks/', { params: { backlog_item_id: backlogItemId } }),
  create: (data) => api.post('/tasks/', data),
  update: (id, data) => api.put(`/tasks/${id}/`, data),
  delete: (id) => api.delete(`/tasks/${id}/`),
};

// WebSocket Stats API
export const websocketApi = {
  getStats: () => api.get('/ws/stats/'),
};

// Health check
export const healthApi = {
  check: () => axios.get('http://localhost:8000/health'),
};

export default api;
