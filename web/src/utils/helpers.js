import { STATUS_CONFIG, ASSIGNEE_TYPE_CONFIG } from './constants';

// Format date for display
export const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
};

// Format date for forms (YYYY-MM-DD)
export const formatDateForInput = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toISOString().split('T')[0];
};

// Get status configuration
export const getStatusConfig = (status) => {
  return STATUS_CONFIG[status] || {
    label: status,
    color: 'bg-gray-100 text-gray-800',
    icon: 'Circle'
  };
};

// Get assignee type configuration
export const getAssigneeTypeConfig = (type) => {
  return ASSIGNEE_TYPE_CONFIG[type] || {
    label: type,
    color: 'bg-gray-100 text-gray-800',
    icon: 'User'
  };
};

// Truncate text
export const truncateText = (text, maxLength = 100) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Generate unique ID
export const generateId = () => {
  return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Debounce function
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle function
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Deep clone object
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

// Check if object is empty
export const isEmpty = (obj) => {
  if (obj == null) return true;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
  return Object.keys(obj).length === 0;
};

// Capitalize first letter
export const capitalize = (str) => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Convert camelCase to Title Case
export const camelToTitle = (str) => {
  if (!str) return '';
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
};

// Sort array by property
export const sortBy = (array, property, direction = 'asc') => {
  return [...array].sort((a, b) => {
    const aVal = a[property];
    const bVal = b[property];
    
    if (aVal < bVal) return direction === 'asc' ? -1 : 1;
    if (aVal > bVal) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

// Group array by property
export const groupBy = (array, property) => {
  return array.reduce((groups, item) => {
    const key = item[property];
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {});
};

// Filter array by search term
export const filterBySearch = (array, searchTerm, searchFields = ['name', 'description']) => {
  if (!searchTerm) return array;
  
  const term = searchTerm.toLowerCase();
  return array.filter(item => 
    searchFields.some(field => 
      item[field]?.toLowerCase().includes(term)
    )
  );
};

// Calculate completion percentage
export const calculateCompletion = (items, statusField = 'status', doneStatus = 'done') => {
  if (!items || items.length === 0) return 0;
  
  const completedItems = items.filter(item => item[statusField] === doneStatus);
  return Math.round((completedItems.length / items.length) * 100);
};

// Get entity hierarchy path
export const getEntityPath = (entity, type) => {
  const paths = [];
  
  switch (type) {
    case 'task':
      if (entity.backlog_item?.feature?.epic?.project) {
        paths.push(entity.backlog_item.feature.epic.project.name);
      }
      if (entity.backlog_item?.feature?.epic) {
        paths.push(entity.backlog_item.feature.epic.name);
      }
      if (entity.backlog_item?.feature) {
        paths.push(entity.backlog_item.feature.name);
      }
      if (entity.backlog_item) {
        paths.push(entity.backlog_item.name);
      }
      break;
    case 'backlog_item':
      if (entity.feature?.epic?.project) {
        paths.push(entity.feature.epic.project.name);
      }
      if (entity.feature?.epic) {
        paths.push(entity.feature.epic.name);
      }
      if (entity.feature) {
        paths.push(entity.feature.name);
      }
      break;
    case 'feature':
      if (entity.epic?.project) {
        paths.push(entity.epic.project.name);
      }
      if (entity.epic) {
        paths.push(entity.epic.name);
      }
      break;
    case 'epic':
      if (entity.project) {
        paths.push(entity.project.name);
      }
      break;
  }
  
  return paths.join(' > ');
};

// Handle API errors
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      message: data.detail || data.message || `Server error (${status})`,
      status,
      type: 'server'
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'Network error - please check your connection',
      type: 'network'
    };
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      type: 'unknown'
    };
  }
};
