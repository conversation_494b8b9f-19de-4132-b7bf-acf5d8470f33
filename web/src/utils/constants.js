// Status enums matching the backend
export const TaskStatus = {
  TODO: 'todo',
  IN_PROGRESS: 'in_progress',
  DONE: 'done'
};

export const BacklogItemStatus = {
  TODO: 'todo',
  READY_FOR_AI: 'ready_for_ai',
  IN_PROGRESS: 'in_progress',
  ACCEPTANCE: 'acceptance',
  DONE: 'done'
};

export const AssigneeType = {
  HUMAN: 'human',
  AI: 'ai'
};

// Status display configurations
export const STATUS_CONFIG = {
  [TaskStatus.TODO]: {
    label: 'To Do',
    color: 'bg-gray-100 text-gray-800',
    icon: 'Circle'
  },
  [TaskStatus.IN_PROGRESS]: {
    label: 'In Progress',
    color: 'bg-blue-100 text-blue-800',
    icon: 'Clock'
  },
  [TaskStatus.DONE]: {
    label: 'Done',
    color: 'bg-green-100 text-green-800',
    icon: 'CheckCircle'
  },
  [BacklogItemStatus.TODO]: {
    label: 'To Do',
    color: 'bg-gray-100 text-gray-800',
    icon: 'Circle'
  },
  [BacklogItemStatus.READY_FOR_AI]: {
    label: 'Ready for AI',
    color: 'bg-purple-100 text-purple-800',
    icon: 'Bot'
  },
  [BacklogItemStatus.IN_PROGRESS]: {
    label: 'In Progress',
    color: 'bg-blue-100 text-blue-800',
    icon: 'Clock'
  },
  [BacklogItemStatus.ACCEPTANCE]: {
    label: 'In Acceptance',
    color: 'bg-yellow-100 text-yellow-800',
    icon: 'Eye'
  },
  [BacklogItemStatus.DONE]: {
    label: 'Done',
    color: 'bg-green-100 text-green-800',
    icon: 'CheckCircle'
  }
};

// Assignee type configurations
export const ASSIGNEE_TYPE_CONFIG = {
  [AssigneeType.HUMAN]: {
    label: 'Human',
    color: 'bg-blue-100 text-blue-800',
    icon: 'User'
  },
  [AssigneeType.AI]: {
    label: 'AI',
    color: 'bg-purple-100 text-purple-800',
    icon: 'Bot'
  }
};

// Entity types for WebSocket events
export const ENTITY_TYPES = {
  PROJECT: 'project',
  EPIC: 'epic',
  FEATURE: 'feature',
  BACKLOG_ITEM: 'backlog_item',
  TASK: 'task'
};

// WebSocket event types
export const WS_EVENT_TYPES = {
  CREATED: 'created',
  UPDATED: 'updated',
  DELETED: 'deleted'
};

// Navigation routes
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  PROJECTS: '/projects',
  PROJECT_DETAIL: '/projects/:id',
  BOARD: '/board',
  WORKFLOW: '/workflow'
};

// API endpoints
export const API_ENDPOINTS = {
  PROJECTS: '/projects',
  EPICS: '/epics',
  FEATURES: '/features',
  BACKLOG_ITEMS: '/backlog-items',
  TASKS: '/tasks',
  WEBSOCKET_STATS: '/ws/stats'
};

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
};

// Drag and drop types
export const DND_TYPES = {
  PROJECT: 'project',
  EPIC: 'epic',
  FEATURE: 'feature',
  BACKLOG_ITEM: 'backlog_item',
  TASK: 'task'
};

// Default pagination
export const DEFAULT_PAGINATION = {
  SKIP: 0,
  LIMIT: 100
};

// Toast types
export const TOAST_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};
