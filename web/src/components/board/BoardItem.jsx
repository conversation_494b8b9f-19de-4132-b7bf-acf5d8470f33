import { useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  GripVertical,
  FolderOpen,
  Target,
  Layers,
  FileText,
  CheckSquare,
  Plus,
  ChevronDown,
  ChevronRight,
  Edit
} from 'lucide-react';
import { Card, StatusBadge, Button, Modal } from '../ui';
import EpicForm from '../forms/EpicForm';
import FeatureForm from '../forms/FeatureForm';
import BacklogItemForm from '../forms/BacklogItemForm';
import TaskForm from '../forms/TaskForm';
import { formatDate } from '../../utils/helpers';

const ENTITY_ICONS = {
  project: FolderOpen,
  epic: Target,
  feature: Layers,
  backlog_item: FileText,
  task: CheckSquare
};

const ENTITY_COLORS = {
  project: 'bg-blue-50 border-blue-200',
  epic: 'bg-purple-50 border-purple-200',
  feature: 'bg-green-50 border-green-200',
  backlog_item: 'bg-yellow-50 border-yellow-200',
  task: 'bg-gray-50 border-gray-200'
};

const BoardItem = ({
  item,
  type,
  childItems = [],
  isExpanded = true,
  onToggleExpand,
  onAddChild,
  level = 0,
  expandedItems,
  onRefresh
}) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  // Helper functions
  const getChildType = () => {
    switch (type) {
      case 'project': return 'epic';
      case 'epic': return 'feature';
      case 'feature': return 'backlog_item';
      case 'backlog_item': return 'task';
      default: return null;
    }
  };

  const getChildTypeName = () => {
    switch (type) {
      case 'project': return 'Epic';
      case 'epic': return 'Feature';
      case 'feature': return 'Backlog Item';
      case 'backlog_item': return 'Task';
      default: return null;
    }
  };

  const getParentIdProp = () => {
    switch (type) {
      case 'project': return 'projectId';
      case 'epic': return 'epicId';
      case 'feature': return 'featureId';
      case 'backlog_item': return 'backlogItemId';
      default: return null;
    }
  };

  const getFormComponent = (isEdit = false) => {
    const targetType = isEdit ? type : getChildType();
    const props = {
      onSuccess: handleFormSuccess,
      onCancel: () => {
        setShowCreateModal(false);
        setShowEditModal(false);
      },
      isModal: true
    };

    if (isEdit) {
      switch (targetType) {
        case 'epic': return <EpicForm epic={item} {...props} />;
        case 'feature': return <FeatureForm feature={item} {...props} />;
        case 'backlog_item': return <BacklogItemForm backlogItem={item} {...props} />;
        case 'task': return <TaskForm task={item} {...props} />;
        default: return null;
      }
    } else {
      const parentIdProp = getParentIdProp();
      const parentProps = parentIdProp ? { [parentIdProp]: item.id } : {};

      switch (targetType) {
        case 'epic': return <EpicForm {...parentProps} {...props} />;
        case 'feature': return <FeatureForm {...parentProps} {...props} />;
        case 'backlog_item': return <BacklogItemForm {...parentProps} {...props} />;
        case 'task': return <TaskForm {...parentProps} {...props} />;
        default: return null;
      }
    }
  };

  const handleFormSuccess = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    if (onRefresh) {
      onRefresh();
    }
  };
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: item.id,
    data: {
      type,
      item,
      level
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const Icon = ENTITY_ICONS[type];
  const hasChildren = childItems && childItems.length > 0;
  const canHaveChildren = type !== 'task';

  return (
    <div
      ref={setNodeRef}
      style={{
        ...style,
        marginLeft: level > 0 ? `${level * 1.5}rem` : '0'
      }}
      className={`
        ${isDragging ? 'opacity-50' : ''}
      `}
    >
      <Card
        className={`
          mb-2 transition-all duration-200 hover:shadow-md group
          ${ENTITY_COLORS[type]}
          ${isDragging ? 'shadow-lg' : ''}
        `}
        padding="sm"
      >
        <div className="flex items-center space-x-3">
          {/* Drag Handle */}
          <div
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-200 rounded"
          >
            <GripVertical className="w-4 h-4 text-gray-400" />
          </div>

          {/* Expand/Collapse Button */}
          {canHaveChildren && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleExpand?.(item.id)}
              className="p-1 w-6 h-6"
            >
              {hasChildren ? (
                isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )
              ) : (
                <div className="w-4 h-4" />
              )}
            </Button>
          )}

          {/* Icon */}
          <div className="flex-shrink-0">
            <Icon className="w-5 h-5 text-gray-600" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {item.name}
                </h4>
                {item.description && (
                  <p className="text-xs text-gray-500 truncate mt-1">
                    {item.description}
                  </p>
                )}
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                {/* Status Badge */}
                <StatusBadge 
                  status={item.status} 
                  size="xs"
                />
                
                {/* Assignee */}
                {item.assignee && (
                  <StatusBadge 
                    status={item.assignee_type} 
                    type="assignee"
                    size="xs"
                  />
                )}
                
                {/* Date */}
                <span className="text-xs text-gray-400">
                  {formatDate(item.created_at)}
                </span>
                
                {/* Action Buttons */}
                <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  {/* Edit Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowEditModal(true)}
                    className="p-1 w-6 h-6"
                    title={`Edit ${type}`}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>

                  {/* Add Child Button */}
                  {canHaveChildren && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowCreateModal(true)}
                      className="p-1 w-6 h-6"
                      title={`Add ${getChildTypeName()}`}
                    >
                      <Plus className="w-3 h-3" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-4">
          {childItems.map(child => {
            const childIsExpanded = expandedItems ? expandedItems.has(child.id) : false;
            return (
              <BoardItem
                key={child.id}
                item={child}
                type={child.type}
                childItems={child.children}
                isExpanded={childIsExpanded}
                onToggleExpand={onToggleExpand}
                onAddChild={onAddChild}
                level={level + 1}
                expandedItems={expandedItems}
                onRefresh={onRefresh}
              />
            );
          })}
        </div>
      )}

      {/* Create Child Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title={`Create ${getChildTypeName()}`}
        size="md"
      >
        {getFormComponent(false)}
      </Modal>

      {/* Edit Item Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={`Edit ${type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}`}
        size="md"
      >
        {getFormComponent(true)}
      </Modal>
    </div>
  );
};

export default BoardItem;
