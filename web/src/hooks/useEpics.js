import { useState, useEffect, useCallback } from 'react';
import { epicsApi } from '../services/api';
import { useApp } from '../contexts/AppContext';
import { handleApiError } from '../utils/helpers';

export const useEpics = () => {
  const { addToast, setLoading, loading } = useApp();
  const [epics, setEpics] = useState([]);
  const [currentEpic, setCurrentEpic] = useState(null);

  // Load all epics
  const loadEpics = useCallback(async () => {
    try {
      setLoading('epics', true);
      const response = await epicsApi.getAll();
      setEpics(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load epics',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('epics', false);
    }
  }, [addToast, setLoading]);

  // Load epics by project
  const loadEpicsByProject = useCallback(async (projectId) => {
    try {
      setLoading('epics', true);
      const response = await epicsApi.getByProject(projectId);
      setEpics(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load epics',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('epics', false);
    }
  }, [addToast, setLoading]);

  // Load a specific epic
  const loadEpic = useCallback(async (id) => {
    try {
      setLoading('epics', true);
      const response = await epicsApi.getById(id);
      setCurrentEpic(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load epic',
        message: errorInfo.message
      });
      return null;
    } finally {
      setLoading('epics', false);
    }
  }, [addToast, setLoading]);

  // Load epic with features
  const loadEpicWithFeatures = useCallback(async (id) => {
    try {
      setLoading('epics', true);
      const response = await epicsApi.getWithFeatures(id);
      setCurrentEpic(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load epic details',
        message: errorInfo.message
      });
      return null;
    } finally {
      setLoading('epics', false);
    }
  }, [addToast, setLoading]);

  // Create a new epic
  const createEpic = useCallback(async (data) => {
    try {
      const response = await epicsApi.create(data);
      const newEpic = response.data;
      
      // Add to local state
      setEpics(prev => [newEpic, ...prev]);
      
      addToast({
        type: 'success',
        title: 'Epic Created',
        message: `${newEpic.name} has been created successfully`
      });
      
      return newEpic;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to create epic',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast]);

  // Update an epic
  const updateEpic = useCallback(async (id, data) => {
    try {
      const response = await epicsApi.update(id, data);
      const updatedEpic = response.data;
      
      // Update local state
      setEpics(prev => 
        prev.map(epic => 
          epic.id === id ? updatedEpic : epic
        )
      );
      
      // Update current epic if it's the one being updated
      if (currentEpic && currentEpic.id === id) {
        setCurrentEpic(updatedEpic);
      }
      
      addToast({
        type: 'success',
        title: 'Epic Updated',
        message: `${updatedEpic.name} has been updated successfully`
      });
      
      return updatedEpic;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to update epic',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentEpic]);

  // Delete an epic
  const deleteEpic = useCallback(async (id) => {
    try {
      await epicsApi.delete(id);
      
      // Remove from local state
      setEpics(prev => prev.filter(epic => epic.id !== id));
      
      // Clear current epic if it's the one being deleted
      if (currentEpic && currentEpic.id === id) {
        setCurrentEpic(null);
      }
      
      addToast({
        type: 'success',
        title: 'Epic Deleted',
        message: 'Epic has been deleted successfully'
      });
      
      return true;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to delete epic',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentEpic]);

  // Search epics
  const searchEpics = useCallback((searchTerm) => {
    if (!searchTerm) return epics;
    
    const term = searchTerm.toLowerCase();
    return epics.filter(epic =>
      epic.name.toLowerCase().includes(term) ||
      (epic.description && epic.description.toLowerCase().includes(term))
    );
  }, [epics]);

  // Get epic by ID from local state
  const getEpicById = useCallback((id) => {
    return epics.find(epic => epic.id === parseInt(id));
  }, [epics]);

  return {
    epics,
    currentEpic,
    loading: loading.epics,
    loadEpics,
    loadEpicsByProject,
    loadEpic,
    loadEpicWithFeatures,
    createEpic,
    updateEpic,
    deleteEpic,
    searchEpics,
    getEpicById,
    setCurrentEpic
  };
};
