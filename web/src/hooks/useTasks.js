import { useState, useEffect, useCallback } from 'react';
import { tasksApi } from '../services/api';
import { useApp } from '../contexts/AppContext';
import { handleApiError } from '../utils/helpers';

export const useTasks = () => {
  const { addToast, setLoading, loading } = useApp();
  const [tasks, setTasks] = useState([]);
  const [currentTask, setCurrentTask] = useState(null);

  // Load all tasks
  const loadTasks = useCallback(async () => {
    try {
      setLoading('tasks', true);
      const response = await tasksApi.getAll();
      setTasks(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load tasks',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('tasks', false);
    }
  }, [addToast, setLoading]);

  // Load tasks by backlog item
  const loadTasksByBacklogItem = useCallback(async (backlogItemId) => {
    try {
      setLoading('tasks', true);
      const response = await tasksApi.getByBacklogItem(backlogItemId);
      setTasks(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load tasks',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('tasks', false);
    }
  }, [addToast, setLoading]);

  // Load a specific task
  const loadTask = useCallback(async (id) => {
    try {
      setLoading('tasks', true);
      const response = await tasksApi.getById(id);
      setCurrentTask(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load task',
        message: errorInfo.message
      });
      return null;
    } finally {
      setLoading('tasks', false);
    }
  }, [addToast, setLoading]);

  // Create a new task
  const createTask = useCallback(async (data) => {
    try {
      const response = await tasksApi.create(data);
      const newTask = response.data;
      
      // Add to local state
      setTasks(prev => [newTask, ...prev]);
      
      addToast({
        type: 'success',
        title: 'Task Created',
        message: `${newTask.name} has been created successfully`
      });
      
      return newTask;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to create task',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast]);

  // Update a task
  const updateTask = useCallback(async (id, data) => {
    try {
      const response = await tasksApi.update(id, data);
      const updatedTask = response.data;
      
      // Update local state
      setTasks(prev => 
        prev.map(task => 
          task.id === id ? updatedTask : task
        )
      );
      
      // Update current task if it's the one being updated
      if (currentTask && currentTask.id === id) {
        setCurrentTask(updatedTask);
      }
      
      addToast({
        type: 'success',
        title: 'Task Updated',
        message: `${updatedTask.name} has been updated successfully`
      });
      
      return updatedTask;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to update task',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentTask]);

  // Delete a task
  const deleteTask = useCallback(async (id) => {
    try {
      await tasksApi.delete(id);
      
      // Remove from local state
      setTasks(prev => prev.filter(task => task.id !== id));
      
      // Clear current task if it's the one being deleted
      if (currentTask && currentTask.id === id) {
        setCurrentTask(null);
      }
      
      addToast({
        type: 'success',
        title: 'Task Deleted',
        message: 'Task has been deleted successfully'
      });
      
      return true;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to delete task',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentTask]);

  // Search tasks
  const searchTasks = useCallback((searchTerm) => {
    if (!searchTerm) return tasks;
    
    const term = searchTerm.toLowerCase();
    return tasks.filter(task =>
      task.name.toLowerCase().includes(term) ||
      (task.description && task.description.toLowerCase().includes(term))
    );
  }, [tasks]);

  // Get task by ID from local state
  const getTaskById = useCallback((id) => {
    return tasks.find(task => task.id === parseInt(id));
  }, [tasks]);

  return {
    tasks,
    currentTask,
    loading: loading.tasks,
    loadTasks,
    loadTasksByBacklogItem,
    loadTask,
    createTask,
    updateTask,
    deleteTask,
    searchTasks,
    getTaskById,
    setCurrentTask
  };
};
