import { useState, useEffect, useCallback } from 'react';
import { backlogItemsApi } from '../services/api';
import { useApp } from '../contexts/AppContext';
import { handleApiError } from '../utils/helpers';

export const useBacklogItems = () => {
  const { addToast, setLoading, loading } = useApp();
  const [backlogItems, setBacklogItems] = useState([]);
  const [currentBacklogItem, setCurrentBacklogItem] = useState(null);

  // Load all backlog items
  const loadBacklogItems = useCallback(async () => {
    try {
      setLoading('backlogItems', true);
      const response = await backlogItemsApi.getAll();
      setBacklogItems(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load backlog items',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('backlogItems', false);
    }
  }, [addToast, setLoading]);

  // Load backlog items by feature
  const loadBacklogItemsByFeature = useCallback(async (featureId) => {
    try {
      setLoading('backlogItems', true);
      const response = await backlogItemsApi.getByFeature(featureId);
      setBacklogItems(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load backlog items',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('backlogItems', false);
    }
  }, [addToast, setLoading]);

  // Load a specific backlog item
  const loadBacklogItem = useCallback(async (id) => {
    try {
      setLoading('backlogItems', true);
      const response = await backlogItemsApi.getById(id);
      setCurrentBacklogItem(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load backlog item',
        message: errorInfo.message
      });
      return null;
    } finally {
      setLoading('backlogItems', false);
    }
  }, [addToast, setLoading]);

  // Load backlog item with tasks
  const loadBacklogItemWithTasks = useCallback(async (id) => {
    try {
      setLoading('backlogItems', true);
      const response = await backlogItemsApi.getWithTasks(id);
      setCurrentBacklogItem(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load backlog item details',
        message: errorInfo.message
      });
      return null;
    } finally {
      setLoading('backlogItems', false);
    }
  }, [addToast, setLoading]);

  // Create a new backlog item
  const createBacklogItem = useCallback(async (data) => {
    try {
      const response = await backlogItemsApi.create(data);
      const newBacklogItem = response.data;
      
      // Add to local state
      setBacklogItems(prev => [newBacklogItem, ...prev]);
      
      addToast({
        type: 'success',
        title: 'Backlog Item Created',
        message: `${newBacklogItem.name} has been created successfully`
      });
      
      return newBacklogItem;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to create backlog item',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast]);

  // Update a backlog item
  const updateBacklogItem = useCallback(async (id, data) => {
    try {
      const response = await backlogItemsApi.update(id, data);
      const updatedBacklogItem = response.data;
      
      // Update local state
      setBacklogItems(prev => 
        prev.map(item => 
          item.id === id ? updatedBacklogItem : item
        )
      );
      
      // Update current backlog item if it's the one being updated
      if (currentBacklogItem && currentBacklogItem.id === id) {
        setCurrentBacklogItem(updatedBacklogItem);
      }
      
      addToast({
        type: 'success',
        title: 'Backlog Item Updated',
        message: `${updatedBacklogItem.name} has been updated successfully`
      });
      
      return updatedBacklogItem;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to update backlog item',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentBacklogItem]);

  // Delete a backlog item
  const deleteBacklogItem = useCallback(async (id) => {
    try {
      await backlogItemsApi.delete(id);
      
      // Remove from local state
      setBacklogItems(prev => prev.filter(item => item.id !== id));
      
      // Clear current backlog item if it's the one being deleted
      if (currentBacklogItem && currentBacklogItem.id === id) {
        setCurrentBacklogItem(null);
      }
      
      addToast({
        type: 'success',
        title: 'Backlog Item Deleted',
        message: 'Backlog item has been deleted successfully'
      });
      
      return true;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to delete backlog item',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentBacklogItem]);

  // Search backlog items
  const searchBacklogItems = useCallback((searchTerm) => {
    if (!searchTerm) return backlogItems;
    
    const term = searchTerm.toLowerCase();
    return backlogItems.filter(item =>
      item.name.toLowerCase().includes(term) ||
      (item.description && item.description.toLowerCase().includes(term))
    );
  }, [backlogItems]);

  // Get backlog item by ID from local state
  const getBacklogItemById = useCallback((id) => {
    return backlogItems.find(item => item.id === parseInt(id));
  }, [backlogItems]);

  return {
    backlogItems,
    currentBacklogItem,
    loading: loading.backlogItems,
    loadBacklogItems,
    loadBacklogItemsByFeature,
    loadBacklogItem,
    loadBacklogItemWithTasks,
    createBacklogItem,
    updateBacklogItem,
    deleteBacklogItem,
    searchBacklogItems,
    getBacklogItemById,
    setCurrentBacklogItem
  };
};
