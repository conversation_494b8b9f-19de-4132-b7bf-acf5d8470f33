import { useState, useEffect, useCallback } from 'react';
import { featuresApi } from '../services/api';
import { useApp } from '../contexts/AppContext';
import { handleApiError } from '../utils/helpers';

export const useFeatures = () => {
  const { addToast, setLoading, loading } = useApp();
  const [features, setFeatures] = useState([]);
  const [currentFeature, setCurrentFeature] = useState(null);

  // Load all features
  const loadFeatures = useCallback(async () => {
    try {
      setLoading('features', true);
      const response = await featuresApi.getAll();
      setFeatures(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load features',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('features', false);
    }
  }, [addToast, setLoading]);

  // Load features by epic
  const loadFeaturesByEpic = useCallback(async (epicId) => {
    try {
      setLoading('features', true);
      const response = await featuresApi.getByEpic(epicId);
      setFeatures(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load features',
        message: errorInfo.message
      });
      return [];
    } finally {
      setLoading('features', false);
    }
  }, [addToast, setLoading]);

  // Load a specific feature
  const loadFeature = useCallback(async (id) => {
    try {
      setLoading('features', true);
      const response = await featuresApi.getById(id);
      setCurrentFeature(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load feature',
        message: errorInfo.message
      });
      return null;
    } finally {
      setLoading('features', false);
    }
  }, [addToast, setLoading]);

  // Load feature with backlog items
  const loadFeatureWithBacklogItems = useCallback(async (id) => {
    try {
      setLoading('features', true);
      const response = await featuresApi.getWithBacklogItems(id);
      setCurrentFeature(response.data);
      return response.data;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load feature details',
        message: errorInfo.message
      });
      return null;
    } finally {
      setLoading('features', false);
    }
  }, [addToast, setLoading]);

  // Create a new feature
  const createFeature = useCallback(async (data) => {
    try {
      const response = await featuresApi.create(data);
      const newFeature = response.data;
      
      // Add to local state
      setFeatures(prev => [newFeature, ...prev]);
      
      addToast({
        type: 'success',
        title: 'Feature Created',
        message: `${newFeature.name} has been created successfully`
      });
      
      return newFeature;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to create feature',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast]);

  // Update a feature
  const updateFeature = useCallback(async (id, data) => {
    try {
      const response = await featuresApi.update(id, data);
      const updatedFeature = response.data;
      
      // Update local state
      setFeatures(prev => 
        prev.map(feature => 
          feature.id === id ? updatedFeature : feature
        )
      );
      
      // Update current feature if it's the one being updated
      if (currentFeature && currentFeature.id === id) {
        setCurrentFeature(updatedFeature);
      }
      
      addToast({
        type: 'success',
        title: 'Feature Updated',
        message: `${updatedFeature.name} has been updated successfully`
      });
      
      return updatedFeature;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to update feature',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentFeature]);

  // Delete a feature
  const deleteFeature = useCallback(async (id) => {
    try {
      await featuresApi.delete(id);
      
      // Remove from local state
      setFeatures(prev => prev.filter(feature => feature.id !== id));
      
      // Clear current feature if it's the one being deleted
      if (currentFeature && currentFeature.id === id) {
        setCurrentFeature(null);
      }
      
      addToast({
        type: 'success',
        title: 'Feature Deleted',
        message: 'Feature has been deleted successfully'
      });
      
      return true;
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to delete feature',
        message: errorInfo.message
      });
      throw error;
    }
  }, [addToast, currentFeature]);

  // Search features
  const searchFeatures = useCallback((searchTerm) => {
    if (!searchTerm) return features;
    
    const term = searchTerm.toLowerCase();
    return features.filter(feature =>
      feature.name.toLowerCase().includes(term) ||
      (feature.description && feature.description.toLowerCase().includes(term))
    );
  }, [features]);

  // Get feature by ID from local state
  const getFeatureById = useCallback((id) => {
    return features.find(feature => feature.id === parseInt(id));
  }, [features]);

  return {
    features,
    currentFeature,
    loading: loading.features,
    loadFeatures,
    loadFeaturesByEpic,
    loadFeature,
    loadFeatureWithBacklogItems,
    createFeature,
    updateFeature,
    deleteFeature,
    searchFeatures,
    getFeatureById,
    setCurrentFeature
  };
};
