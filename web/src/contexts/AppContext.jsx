import { createContext, useContext, useReducer, useEffect } from 'react';
import websocketManager from '../services/websocket';
import { generateId } from '../utils/helpers';

// Initial state
const initialState = {
  // WebSocket connection
  wsStatus: 'disconnected',
  wsStats: null,
  
  // Toast notifications
  toasts: [],
  
  // Loading states
  loading: {
    global: false,
    projects: false,
    epics: false,
    features: false,
    backlogItems: false,
    tasks: false
  },
  
  // Data cache
  projects: [],
  currentProject: null,
  
  // UI state
  sidebarOpen: false,
  
  // Error state
  error: null
};

// Action types
const ActionTypes = {
  SET_WS_STATUS: 'SET_WS_STATUS',
  SET_WS_STATS: 'SET_WS_STATS',
  ADD_TOAST: 'ADD_TOAST',
  REMOVE_TOAST: 'REMOVE_TOAST',
  SET_LOADING: 'SET_LOADING',
  SET_PROJECTS: 'SET_PROJECTS',
  SET_CURRENT_PROJECT: 'SET_CURRENT_PROJECT',
  TOGGLE_SIDEBAR: 'TOGGLE_SIDEBAR',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Reducer
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_WS_STATUS:
      return { ...state, wsStatus: action.payload };
      
    case ActionTypes.SET_WS_STATS:
      return { ...state, wsStats: action.payload };
      
    case ActionTypes.ADD_TOAST:
      return { 
        ...state, 
        toasts: [...state.toasts, { id: generateId(), ...action.payload }] 
      };
      
    case ActionTypes.REMOVE_TOAST:
      return { 
        ...state, 
        toasts: state.toasts.filter(toast => toast.id !== action.payload) 
      };
      
    case ActionTypes.SET_LOADING:
      return { 
        ...state, 
        loading: { ...state.loading, [action.payload.key]: action.payload.value } 
      };
      
    case ActionTypes.SET_PROJECTS:
      return { ...state, projects: action.payload };
      
    case ActionTypes.SET_CURRENT_PROJECT:
      return { ...state, currentProject: action.payload };
      
    case ActionTypes.TOGGLE_SIDEBAR:
      return { ...state, sidebarOpen: !state.sidebarOpen };
      
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload };
      
    case ActionTypes.CLEAR_ERROR:
      return { ...state, error: null };
      
    default:
      return state;
  }
};

// Context
const AppContext = createContext();

// Provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // WebSocket setup
  useEffect(() => {
    // Connection status listener
    websocketManager.on('connection', (data) => {
      dispatch({ type: ActionTypes.SET_WS_STATUS, payload: data.status });
    });

    // Entity event listener
    websocketManager.on('entity_event', (data) => {
      // Handle real-time updates here
      console.log('Entity event received:', data);
      
      // Show toast for entity updates
      if (data.event_type === 'created') {
        addToast({
          type: 'success',
          title: 'Item Created',
          message: `New ${data.entity_type} has been created`
        });
      }
    });

    // Connect to WebSocket
    websocketManager.connect();

    return () => {
      websocketManager.disconnect();
    };
  }, []);

  // Action creators
  const setWsStatus = (status) => {
    dispatch({ type: ActionTypes.SET_WS_STATUS, payload: status });
  };

  const setWsStats = (stats) => {
    dispatch({ type: ActionTypes.SET_WS_STATS, payload: stats });
  };

  const addToast = (toast) => {
    dispatch({ type: ActionTypes.ADD_TOAST, payload: toast });
  };

  const removeToast = (id) => {
    dispatch({ type: ActionTypes.REMOVE_TOAST, payload: id });
  };

  const setLoading = (key, value) => {
    dispatch({ type: ActionTypes.SET_LOADING, payload: { key, value } });
  };

  const setProjects = (projects) => {
    dispatch({ type: ActionTypes.SET_PROJECTS, payload: projects });
  };

  const setCurrentProject = (project) => {
    dispatch({ type: ActionTypes.SET_CURRENT_PROJECT, payload: project });
  };

  const toggleSidebar = () => {
    dispatch({ type: ActionTypes.TOGGLE_SIDEBAR });
  };

  const setError = (error) => {
    dispatch({ type: ActionTypes.SET_ERROR, payload: error });
  };

  const clearError = () => {
    dispatch({ type: ActionTypes.CLEAR_ERROR });
  };

  const value = {
    ...state,
    setWsStatus,
    setWsStats,
    addToast,
    removeToast,
    setLoading,
    setProjects,
    setCurrentProject,
    toggleSidebar,
    setError,
    clearError
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
