import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, FileText, Plus } from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  LoadingSpinner,
  Modal,
  StatusBadge
} from '../components/ui';
import BacklogItemForm from '../components/forms/BacklogItemForm';
import TaskForm from '../components/forms/TaskForm';
import { useBacklogItems } from '../hooks/useBacklogItems';
import { useTasks } from '../hooks/useTasks';
import { formatDate } from '../utils/helpers';

const BacklogItemDetail = () => {
  const { id } = useParams();
  const { currentBacklogItem, loading: backlogItemLoading, loadBacklogItem } = useBacklogItems();
  const { tasks, loading: tasksLoading, loadTasksByBacklogItem } = useTasks();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);

  useEffect(() => {
    if (id) {
      loadBacklogItem(id);
      loadTasksByBacklogItem(id);
    }
  }, [id]);

  const handleEditSuccess = () => {
    setShowEditModal(false);
    if (id) {
      loadBacklogItem(id);
    }
  };

  const handleCreateTaskSuccess = () => {
    setShowCreateTaskModal(false);
    loadTasksByBacklogItem(id);
  };

  if (backlogItemLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading backlog item..." />
      </div>
    );
  }

  if (!currentBacklogItem) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium text-gray-900 mb-2">Backlog item not found</h3>
        <p className="text-gray-500 mb-6">The backlog item you're looking for doesn't exist.</p>
        <Link to="/projects">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <Link to="/projects" className="hover:text-gray-700">Projects</Link>
        <span>/</span>
        {currentBacklogItem.feature?.epic?.project && (
          <>
            <Link to={`/projects/${currentBacklogItem.feature.epic.project.id}`} className="hover:text-gray-700">
              {currentBacklogItem.feature.epic.project.name}
            </Link>
            <span>/</span>
          </>
        )}
        {currentBacklogItem.feature?.epic && (
          <>
            <Link to={`/epics/${currentBacklogItem.feature.epic.id}`} className="hover:text-gray-700">
              {currentBacklogItem.feature.epic.name}
            </Link>
            <span>/</span>
          </>
        )}
        {currentBacklogItem.feature && (
          <>
            <Link to={`/features/${currentBacklogItem.feature.id}`} className="hover:text-gray-700">
              {currentBacklogItem.feature.name}
            </Link>
            <span>/</span>
          </>
        )}
        <span className="text-gray-900">{currentBacklogItem.name}</span>
      </div>

      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-start space-x-4">
          <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
            <FileText className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{currentBacklogItem.name}</h1>
            {currentBacklogItem.description && (
              <p className="mt-2 text-gray-600">{currentBacklogItem.description}</p>
            )}
            <div className="flex items-center space-x-4 mt-2">
              <StatusBadge status={currentBacklogItem.status} />
              {currentBacklogItem.assignee && (
                <StatusBadge 
                  status={currentBacklogItem.assignee_type} 
                  type="assignee"
                />
              )}
              <span className="text-sm text-gray-500">
                Created {formatDate(currentBacklogItem.created_at)}
                {currentBacklogItem.updated_at && currentBacklogItem.updated_at !== currentBacklogItem.created_at && (
                  <span> • Updated {formatDate(currentBacklogItem.updated_at)}</span>
                )}
              </span>
            </div>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link to={currentBacklogItem.feature ? `/features/${currentBacklogItem.feature.id}` : '/projects'}>
            <Button variant="secondary">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Feature
            </Button>
          </Link>
          <Button onClick={() => setShowEditModal(true)}>
            Edit Backlog Item
          </Button>
        </div>
      </div>

      {/* Backlog Item Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Tasks</CardTitle>
              <Button 
                size="sm"
                onClick={() => setShowCreateTaskModal(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Task
              </Button>
            </CardHeader>
            <CardContent>
              {tasksLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner text="Loading tasks..." />
                </div>
              ) : tasks.length > 0 ? (
                <div className="space-y-4">
                  {tasks.map((task) => (
                    <div key={task.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {task.name}
                          </h3>
                          {task.description && (
                            <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                          )}
                          <div className="flex items-center space-x-2 mt-2">
                            <StatusBadge status={task.status} size="xs" />
                            {task.assignee && (
                              <StatusBadge 
                                status={task.assignee_type} 
                                type="assignee"
                                size="xs"
                              />
                            )}
                            <span className="text-xs text-gray-500">
                              {formatDate(task.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No tasks yet. Create your first task to get started.</p>
                  <Button 
                    className="mt-4"
                    onClick={() => setShowCreateTaskModal(true)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Task
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Backlog Item Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Tasks</span>
                  <span className="text-sm font-medium">{tasks.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <StatusBadge status={currentBacklogItem.status} size="xs" />
                </div>
                {currentBacklogItem.assignee && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Assignee</span>
                    <div className="flex items-center space-x-1">
                      <StatusBadge 
                        status={currentBacklogItem.assignee_type} 
                        type="assignee"
                        size="xs"
                      />
                      <span className="text-sm">{currentBacklogItem.assignee}</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button 
                  variant="secondary" 
                  className="w-full justify-start"
                  onClick={() => setShowCreateTaskModal(true)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Task
                </Button>
                <Link to="/board">
                  <Button variant="secondary" className="w-full justify-start">
                    View Board
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Backlog Item Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Backlog Item"
        size="md"
      >
        <BacklogItemForm
          backlogItem={currentBacklogItem}
          onSuccess={handleEditSuccess}
          onCancel={() => setShowEditModal(false)}
          isModal={true}
        />
      </Modal>

      {/* Create Task Modal */}
      <Modal
        isOpen={showCreateTaskModal}
        onClose={() => setShowCreateTaskModal(false)}
        title="Create Task"
        size="md"
      >
        <TaskForm
          backlogItemId={parseInt(id)}
          onSuccess={handleCreateTaskSuccess}
          onCancel={() => setShowCreateTaskModal(false)}
          isModal={true}
        />
      </Modal>
    </div>
  );
};

export default BacklogItemDetail;
