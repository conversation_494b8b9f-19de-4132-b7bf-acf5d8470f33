import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Target, Plus } from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  LoadingSpinner,
  Modal,
  StatusBadge
} from '../components/ui';
import EpicForm from '../components/forms/EpicForm';
import FeatureForm from '../components/forms/FeatureForm';
import { useEpics } from '../hooks/useEpics';
import { useFeatures } from '../hooks/useFeatures';
import { formatDate } from '../utils/helpers';

const EpicDetail = () => {
  const { id } = useParams();
  const { currentEpic, loading: epicLoading, loadEpic } = useEpics();
  const { features, loading: featuresLoading, loadFeaturesByEpic } = useFeatures();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCreateFeatureModal, setShowCreateFeatureModal] = useState(false);

  useEffect(() => {
    if (id) {
      loadEpic(id);
      loadFeaturesByEpic(id);
    }
  }, [id]);

  const handleEditSuccess = () => {
    setShowEditModal(false);
    if (id) {
      loadEpic(id);
    }
  };

  const handleCreateFeatureSuccess = () => {
    setShowCreateFeatureModal(false);
    loadFeaturesByEpic(id);
  };

  if (epicLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading epic..." />
      </div>
    );
  }

  if (!currentEpic) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium text-gray-900 mb-2">Epic not found</h3>
        <p className="text-gray-500 mb-6">The epic you're looking for doesn't exist.</p>
        <Link to="/projects">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <Link to="/projects" className="hover:text-gray-700">Projects</Link>
        <span>/</span>
        {currentEpic.project && (
          <>
            <Link to={`/projects/${currentEpic.project.id}`} className="hover:text-gray-700">
              {currentEpic.project.name}
            </Link>
            <span>/</span>
          </>
        )}
        <span className="text-gray-900">{currentEpic.name}</span>
      </div>

      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-start space-x-4">
          <div className="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center">
            <Target className="w-8 h-8 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{currentEpic.name}</h1>
            {currentEpic.description && (
              <p className="mt-2 text-gray-600">{currentEpic.description}</p>
            )}
            <div className="flex items-center space-x-4 mt-2">
              <StatusBadge status={currentEpic.status} />
              {currentEpic.assignee && (
                <StatusBadge 
                  status={currentEpic.assignee_type} 
                  type="assignee"
                />
              )}
              <span className="text-sm text-gray-500">
                Created {formatDate(currentEpic.created_at)}
                {currentEpic.updated_at && currentEpic.updated_at !== currentEpic.created_at && (
                  <span> • Updated {formatDate(currentEpic.updated_at)}</span>
                )}
              </span>
            </div>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link to={currentEpic.project ? `/projects/${currentEpic.project.id}` : '/projects'}>
            <Button variant="secondary">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          <Button onClick={() => setShowEditModal(true)}>
            Edit Epic
          </Button>
        </div>
      </div>

      {/* Epic Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Features</CardTitle>
              <Button 
                size="sm"
                onClick={() => setShowCreateFeatureModal(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Feature
              </Button>
            </CardHeader>
            <CardContent>
              {featuresLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner text="Loading features..." />
                </div>
              ) : features.length > 0 ? (
                <div className="space-y-4">
                  {features.map((feature) => (
                    <div key={feature.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Link 
                            to={`/features/${feature.id}`}
                            className="text-lg font-medium text-gray-900 hover:text-blue-600"
                          >
                            {feature.name}
                          </Link>
                          {feature.description && (
                            <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                          )}
                          <div className="flex items-center space-x-2 mt-2">
                            <StatusBadge status={feature.status} size="xs" />
                            {feature.assignee && (
                              <StatusBadge 
                                status={feature.assignee_type} 
                                type="assignee"
                                size="xs"
                              />
                            )}
                            <span className="text-xs text-gray-500">
                              {formatDate(feature.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No features yet. Create your first feature to get started.</p>
                  <Button 
                    className="mt-4"
                    onClick={() => setShowCreateFeatureModal(true)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Feature
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Epic Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Features</span>
                  <span className="text-sm font-medium">{features.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <StatusBadge status={currentEpic.status} size="xs" />
                </div>
                {currentEpic.assignee && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Assignee</span>
                    <div className="flex items-center space-x-1">
                      <StatusBadge 
                        status={currentEpic.assignee_type} 
                        type="assignee"
                        size="xs"
                      />
                      <span className="text-sm">{currentEpic.assignee}</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button 
                  variant="secondary" 
                  className="w-full justify-start"
                  onClick={() => setShowCreateFeatureModal(true)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Feature
                </Button>
                <Link to="/board">
                  <Button variant="secondary" className="w-full justify-start">
                    View Board
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Epic Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Epic"
        size="md"
      >
        <EpicForm
          epic={currentEpic}
          onSuccess={handleEditSuccess}
          onCancel={() => setShowEditModal(false)}
          isModal={true}
        />
      </Modal>

      {/* Create Feature Modal */}
      <Modal
        isOpen={showCreateFeatureModal}
        onClose={() => setShowCreateFeatureModal(false)}
        title="Create Feature"
        size="md"
      >
        <FeatureForm
          epicId={parseInt(id)}
          onSuccess={handleCreateFeatureSuccess}
          onCancel={() => setShowCreateFeatureModal(false)}
          isModal={true}
        />
      </Modal>
    </div>
  );
};

export default EpicDetail;
