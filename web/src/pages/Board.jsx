import { useState } from 'react';
import {
  CheckSquare,
  Plus,
  Expand,
  Minimize2,
  Filter,
  Search
} from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  LoadingSpinner,
  Input,
  Modal
} from '../components/ui';
import BoardItem from '../components/board/BoardItem';
import ProjectForm from '../components/forms/ProjectForm';
import { useBoardData } from '../hooks/useBoardData';

const Board = () => {
  const {
    boardData,
    loading,
    expandedItems,
    toggleExpanded,
    expandAll,
    collapseAll,
    handleDragEnd,
    loadBoardData
  } = useBoardData();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Flatten all items for sortable context
  const getAllItemIds = (items) => {
    const ids = [];
    items.forEach(item => {
      ids.push(item.id);
      if (item.children && item.children.length > 0) {
        ids.push(...getAllItemIds(item.children));
      }
    });
    return ids;
  };

  const allItemIds = getAllItemIds(boardData);

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    loadBoardData(); // Refresh board data
  };

  // Render board item
  const renderBoardItem = (item, level = 0) => {
    const isExpanded = expandedItems.has(item.id);

    return (
      <BoardItem
        key={item.id}
        item={item}
        type={item.type}
        childItems={item.children}
        isExpanded={isExpanded}
        onToggleExpand={toggleExpanded}
        level={level}
        expandedItems={expandedItems}
        onRefresh={loadBoardData}
      />
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading board..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Board</h1>
          <p className="mt-1 text-sm text-gray-500">
            Hierarchical view of your projects with drag-and-drop functionality
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary" onClick={collapseAll}>
            <Minimize2 className="w-4 h-4 mr-2" />
            Collapse All
          </Button>
          <Button variant="secondary" onClick={expandAll}>
            <Expand className="w-4 h-4 mr-2" />
            Expand All
          </Button>
          <Button onClick={() => setShowCreateModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Project
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex space-x-4">
        <div className="flex-1 max-w-md">
          <Input
            placeholder="Search items..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            icon={Search}
          />
        </div>
        <Button variant="secondary">
          <Filter className="w-4 h-4 mr-2" />
          Filter
        </Button>
      </div>

      {/* Board Content */}
      {boardData.length > 0 ? (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={allItemIds}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-2">
              {boardData.map(project => renderBoardItem(project))}
            </div>
          </SortableContext>
        </DndContext>
      ) : (
        <Card>
          <CardContent className="p-8">
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckSquare className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">
                No projects found
              </h3>
              <p className="text-gray-500 mb-6">
                Create your first project to start organizing your backlog.
              </p>
              <Button onClick={() => setShowCreateModal(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Project
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Create Project Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create New Project"
        size="md"
      >
        <ProjectForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setShowCreateModal(false)}
          isModal={true}
        />
      </Modal>
    </div>
  );
};

export default Board;
