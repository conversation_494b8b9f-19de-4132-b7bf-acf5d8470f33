import { useState, useEffect } from 'react';
import { Bo<PERSON>, Plus, Clock, CheckCircle, Eye, ArrowRight } from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  LoadingSpinner,
  StatusBadge
} from '../components/ui';
import { backlogItemsApi } from '../services/api';
import { useApp } from '../contexts/AppContext';
import { formatDate, handleApiError } from '../utils/helpers';
import { BacklogItemStatus } from '../utils/constants';

const Workflow = () => {
  const { addToast, setLoading, loading } = useApp();
  const [workflowItems, setWorkflowItems] = useState({
    readyForAI: [],
    inProgress: [],
    inAcceptance: []
  });

  useEffect(() => {
    loadWorkflowData();
  }, []);

  const loadWorkflowData = async () => {
    try {
      setLoading('backlogItems', true);

      // Load all backlog items
      const response = await backlogItemsApi.getAll();
      const allItems = response.data;

      // Group by workflow status
      const readyForAI = allItems.filter(item => item.status === BacklogItemStatus.READY_FOR_AI);
      const inProgress = allItems.filter(item => item.status === BacklogItemStatus.IN_PROGRESS);
      const inAcceptance = allItems.filter(item => item.status === BacklogItemStatus.ACCEPTANCE);

      setWorkflowItems({
        readyForAI,
        inProgress,
        inAcceptance
      });

    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to load workflow data',
        message: errorInfo.message
      });
    } finally {
      setLoading('backlogItems', false);
    }
  };

  const handleStatusChange = async (itemId, newStatus) => {
    try {
      await backlogItemsApi.update(itemId, { status: newStatus });
      addToast({
        type: 'success',
        title: 'Status Updated',
        message: 'Item status has been updated successfully'
      });
      loadWorkflowData(); // Refresh data
    } catch (error) {
      const errorInfo = handleApiError(error);
      addToast({
        type: 'error',
        title: 'Failed to update status',
        message: errorInfo.message
      });
    }
  };

  const WorkflowColumn = ({ title, items, status, icon: Icon, color, nextStatus, nextLabel }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon className={`w-5 h-5 mr-2 ${color}`} />
          {title}
          <span className="ml-2 text-sm font-normal text-gray-500">({items.length})</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {items.length > 0 ? (
          <div className="space-y-3">
            {items.map((item) => (
              <div
                key={item.id}
                className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 mb-1">
                      {item.name}
                    </h4>
                    {item.description && (
                      <p className="text-xs text-gray-600 mb-2">
                        {item.description.length > 100
                          ? `${item.description.substring(0, 100)}...`
                          : item.description
                        }
                      </p>
                    )}
                    <div className="flex items-center space-x-2">
                      <StatusBadge status={item.status} size="xs" />
                      {item.assignee && (
                        <StatusBadge
                          status={item.assignee_type}
                          type="assignee"
                          size="xs"
                        />
                      )}
                      <span className="text-xs text-gray-400">
                        {formatDate(item.created_at)}
                      </span>
                    </div>
                  </div>
                  {nextStatus && (
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleStatusChange(item.id, nextStatus)}
                      className="ml-3"
                    >
                      <ArrowRight className="w-3 h-3 mr-1" />
                      {nextLabel}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Icon className={`w-12 h-12 mx-auto mb-3 ${color} opacity-50`} />
            <p className="text-sm text-gray-500">No items in {title.toLowerCase()}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loading.backlogItems) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading workflow data..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">AI Workflow</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor and manage AI-powered task processing
          </p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Queue for AI
        </Button>
      </div>

      {/* Workflow Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                  <Bot className="w-5 h-5 text-purple-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Ready for AI</p>
                <p className="text-2xl font-semibold text-gray-900">{workflowItems.readyForAI.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                  <Clock className="w-5 h-5 text-blue-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">In Progress</p>
                <p className="text-2xl font-semibold text-gray-900">{workflowItems.inProgress.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                  <Eye className="w-5 h-5 text-yellow-600" />
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">In Acceptance</p>
                <p className="text-2xl font-semibold text-gray-900">{workflowItems.inAcceptance.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflow Columns */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <WorkflowColumn
          title="Ready for AI"
          items={workflowItems.readyForAI}
          status={BacklogItemStatus.READY_FOR_AI}
          icon={Bot}
          color="text-purple-600"
          nextStatus={BacklogItemStatus.IN_PROGRESS}
          nextLabel="Start Processing"
        />

        <WorkflowColumn
          title="In Progress"
          items={workflowItems.inProgress}
          status={BacklogItemStatus.IN_PROGRESS}
          icon={Clock}
          color="text-blue-600"
          nextStatus={BacklogItemStatus.ACCEPTANCE}
          nextLabel="Move to Acceptance"
        />

        <WorkflowColumn
          title="In Acceptance"
          items={workflowItems.inAcceptance}
          status={BacklogItemStatus.ACCEPTANCE}
          icon={Eye}
          color="text-yellow-600"
          nextStatus={BacklogItemStatus.DONE}
          nextLabel="Mark as Done"
        />
      </div>
    </div>
  );
};

export default Workflow;
