import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Layers, Plus } from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  LoadingSpinner,
  Modal,
  StatusBadge
} from '../components/ui';
import FeatureForm from '../components/forms/FeatureForm';
import BacklogItemForm from '../components/forms/BacklogItemForm';
import { useFeatures } from '../hooks/useFeatures';
import { useBacklogItems } from '../hooks/useBacklogItems';
import { formatDate } from '../utils/helpers';

const FeatureDetail = () => {
  const { id } = useParams();
  const { currentFeature, loading: featureLoading, loadFeature } = useFeatures();
  const { backlogItems, loading: backlogItemsLoading, loadBacklogItemsByFeature } = useBacklogItems();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCreateBacklogItemModal, setShowCreateBacklogItemModal] = useState(false);

  useEffect(() => {
    if (id) {
      loadFeature(id);
      loadBacklogItemsByFeature(id);
    }
  }, [id]);

  const handleEditSuccess = () => {
    setShowEditModal(false);
    if (id) {
      loadFeature(id);
    }
  };

  const handleCreateBacklogItemSuccess = () => {
    setShowCreateBacklogItemModal(false);
    loadBacklogItemsByFeature(id);
  };

  if (featureLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading feature..." />
      </div>
    );
  }

  if (!currentFeature) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium text-gray-900 mb-2">Feature not found</h3>
        <p className="text-gray-500 mb-6">The feature you're looking for doesn't exist.</p>
        <Link to="/projects">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <Link to="/projects" className="hover:text-gray-700">Projects</Link>
        <span>/</span>
        {currentFeature.epic?.project && (
          <>
            <Link to={`/projects/${currentFeature.epic.project.id}`} className="hover:text-gray-700">
              {currentFeature.epic.project.name}
            </Link>
            <span>/</span>
          </>
        )}
        {currentFeature.epic && (
          <>
            <Link to={`/epics/${currentFeature.epic.id}`} className="hover:text-gray-700">
              {currentFeature.epic.name}
            </Link>
            <span>/</span>
          </>
        )}
        <span className="text-gray-900">{currentFeature.name}</span>
      </div>

      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-start space-x-4">
          <div className="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center">
            <Layers className="w-8 h-8 text-green-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{currentFeature.name}</h1>
            {currentFeature.description && (
              <p className="mt-2 text-gray-600">{currentFeature.description}</p>
            )}
            <div className="flex items-center space-x-4 mt-2">
              <StatusBadge status={currentFeature.status} />
              {currentFeature.assignee && (
                <StatusBadge 
                  status={currentFeature.assignee_type} 
                  type="assignee"
                />
              )}
              <span className="text-sm text-gray-500">
                Created {formatDate(currentFeature.created_at)}
                {currentFeature.updated_at && currentFeature.updated_at !== currentFeature.created_at && (
                  <span> • Updated {formatDate(currentFeature.updated_at)}</span>
                )}
              </span>
            </div>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link to={currentFeature.epic ? `/epics/${currentFeature.epic.id}` : '/projects'}>
            <Button variant="secondary">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Epic
            </Button>
          </Link>
          <Button onClick={() => setShowEditModal(true)}>
            Edit Feature
          </Button>
        </div>
      </div>

      {/* Feature Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Backlog Items</CardTitle>
              <Button 
                size="sm"
                onClick={() => setShowCreateBacklogItemModal(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Backlog Item
              </Button>
            </CardHeader>
            <CardContent>
              {backlogItemsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner text="Loading backlog items..." />
                </div>
              ) : backlogItems.length > 0 ? (
                <div className="space-y-4">
                  {backlogItems.map((item) => (
                    <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Link 
                            to={`/backlog-items/${item.id}`}
                            className="text-lg font-medium text-gray-900 hover:text-blue-600"
                          >
                            {item.name}
                          </Link>
                          {item.description && (
                            <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                          )}
                          <div className="flex items-center space-x-2 mt-2">
                            <StatusBadge status={item.status} size="xs" />
                            {item.assignee && (
                              <StatusBadge 
                                status={item.assignee_type} 
                                type="assignee"
                                size="xs"
                              />
                            )}
                            <span className="text-xs text-gray-500">
                              {formatDate(item.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No backlog items yet. Create your first backlog item to get started.</p>
                  <Button 
                    className="mt-4"
                    onClick={() => setShowCreateBacklogItemModal(true)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Backlog Item
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Feature Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Backlog Items</span>
                  <span className="text-sm font-medium">{backlogItems.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <StatusBadge status={currentFeature.status} size="xs" />
                </div>
                {currentFeature.assignee && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Assignee</span>
                    <div className="flex items-center space-x-1">
                      <StatusBadge 
                        status={currentFeature.assignee_type} 
                        type="assignee"
                        size="xs"
                      />
                      <span className="text-sm">{currentFeature.assignee}</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button 
                  variant="secondary" 
                  className="w-full justify-start"
                  onClick={() => setShowCreateBacklogItemModal(true)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Backlog Item
                </Button>
                <Link to="/board">
                  <Button variant="secondary" className="w-full justify-start">
                    View Board
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Feature Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Feature"
        size="md"
      >
        <FeatureForm
          feature={currentFeature}
          onSuccess={handleEditSuccess}
          onCancel={() => setShowEditModal(false)}
          isModal={true}
        />
      </Modal>

      {/* Create Backlog Item Modal */}
      <Modal
        isOpen={showCreateBacklogItemModal}
        onClose={() => setShowCreateBacklogItemModal(false)}
        title="Create Backlog Item"
        size="md"
      >
        <BacklogItemForm
          featureId={parseInt(id)}
          onSuccess={handleCreateBacklogItemSuccess}
          onCancel={() => setShowCreateBacklogItemModal(false)}
          isModal={true}
        />
      </Modal>
    </div>
  );
};

export default FeatureDetail;
