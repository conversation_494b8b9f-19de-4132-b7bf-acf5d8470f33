import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, FolderOpen, Plus } from 'lucide-react';
import {
  Card,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  <PERSON>ton,
  LoadingSpinner,
  Modal,
  StatusBadge
} from '../components/ui';
import ProjectForm from '../components/forms/ProjectForm';
import EpicForm from '../components/forms/EpicForm';
import { useProjects } from '../hooks/useProjects';
import { useEpics } from '../hooks/useEpics';
import { formatDate } from '../utils/helpers';

const ProjectDetail = () => {
  const { id } = useParams();
  const { currentProject, loading, loadProject } = useProjects();
  const { epics, loading: epicsLoading, loadEpicsByProject } = useEpics();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showCreateEpicModal, setShowCreateEpicModal] = useState(false);

  useEffect(() => {
    if (id) {
      loadProject(id);
      loadEpicsByProject(id);
    }
  }, [id]); // Remove loadProject from dependencies to prevent infinite loop

  const handleEditSuccess = () => {
    setShowEditModal(false);
    // Reload the project to get updated data
    if (id) {
      loadProject(id);
    }
  };

  const handleCreateEpicSuccess = () => {
    setShowCreateEpicModal(false);
    // Reload epics to show the new one
    if (id) {
      loadEpicsByProject(id);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading project..." />
      </div>
    );
  }

  if (!currentProject) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-medium text-gray-900 mb-2">Project not found</h3>
        <p className="text-gray-500 mb-6">The project you're looking for doesn't exist.</p>
        <Link to="/projects">
          <Button>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <Link to="/projects" className="hover:text-gray-700">Projects</Link>
        <span>/</span>
        <span className="text-gray-900">{currentProject.name}</span>
      </div>

      {/* Page Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-start space-x-4">
          <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
            <FolderOpen className="w-8 h-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{currentProject.name}</h1>
            {currentProject.description && (
              <p className="mt-2 text-gray-600">{currentProject.description}</p>
            )}
            <p className="mt-2 text-sm text-gray-500">
              Created {formatDate(currentProject.created_at)}
              {currentProject.updated_at && currentProject.updated_at !== currentProject.created_at && (
                <span> • Updated {formatDate(currentProject.updated_at)}</span>
              )}
            </p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link to="/projects">
            <Button variant="secondary">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Projects
            </Button>
          </Link>
          <Button onClick={() => setShowEditModal(true)}>
            Edit Project
          </Button>
        </div>
      </div>

      {/* Project Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Epics</CardTitle>
              <Button
                size="sm"
                onClick={() => setShowCreateEpicModal(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Epic
              </Button>
            </CardHeader>
            <CardContent>
              {epicsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner text="Loading epics..." />
                </div>
              ) : epics.length > 0 ? (
                <div className="space-y-4">
                  {epics.map((epic) => (
                    <div key={epic.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Link
                            to={`/epics/${epic.id}`}
                            className="text-lg font-medium text-gray-900 hover:text-blue-600"
                          >
                            {epic.name}
                          </Link>
                          {epic.description && (
                            <p className="text-sm text-gray-600 mt-1">{epic.description}</p>
                          )}
                          <div className="flex items-center space-x-2 mt-2">
                            <StatusBadge status={epic.status} size="xs" />
                            {epic.assignee && (
                              <StatusBadge
                                status={epic.assignee_type}
                                type="assignee"
                                size="xs"
                              />
                            )}
                            <span className="text-xs text-gray-500">
                              {formatDate(epic.created_at)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No epics yet. Create your first epic to get started.</p>
                  <Button
                    className="mt-4"
                    onClick={() => setShowCreateEpicModal(true)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create Epic
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Project Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Epics</span>
                  <span className="text-sm font-medium">{epics.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Features</span>
                  <span className="text-sm font-medium">0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Backlog Items</span>
                  <span className="text-sm font-medium">0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Tasks</span>
                  <span className="text-sm font-medium">0</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button
                  variant="secondary"
                  className="w-full justify-start"
                  onClick={() => setShowCreateEpicModal(true)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Create Epic
                </Button>
                <Link to="/board">
                  <Button variant="secondary" className="w-full justify-start">
                    View Board
                  </Button>
                </Link>
                <Button variant="secondary" className="w-full justify-start">
                  Export Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Project Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Project"
        size="md"
      >
        <ProjectForm
          project={currentProject}
          onSuccess={handleEditSuccess}
          onCancel={() => setShowEditModal(false)}
          isModal={true}
        />
      </Modal>

      {/* Create Epic Modal */}
      <Modal
        isOpen={showCreateEpicModal}
        onClose={() => setShowCreateEpicModal(false)}
        title="Create Epic"
        size="md"
      >
        <EpicForm
          projectId={parseInt(id)}
          onSuccess={handleCreateEpicSuccess}
          onCancel={() => setShowCreateEpicModal(false)}
          isModal={true}
        />
      </Modal>
    </div>
  );
};

export default ProjectDetail;
