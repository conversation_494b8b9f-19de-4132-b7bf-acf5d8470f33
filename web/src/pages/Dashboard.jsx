import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  CheckCircle,
  Eye,
  Plus,
  ArrowRight,
  TrendingUp
} from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  LoadingSpinner,
  StatusBadge,
  Modal
} from '../components/ui';
import ProjectForm from '../components/forms/ProjectForm';
import { useProjects } from '../hooks/useProjects';
import { formatDate } from '../utils/helpers';

const Dashboard = () => {
  const { projects, loading } = useProjects();
  const [showCreateModal, setShowCreateModal] = useState(false);

  const stats = {
    projectsCount: projects.length,
    aiProcessing: 0, // TODO: Calculate from actual data
    completedToday: 0, // TODO: Calculate from actual data
    inAcceptance: 0 // TODO: Calculate from actual data
  };

  const recentProjects = projects.slice(0, 5); // Show 5 most recent

  const handleCreateSuccess = () => {
    setShowCreateModal(false);
  };

  const StatCard = ({ title, value, icon: Icon, color, trend }) => (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`w-8 h-8 ${color} rounded-md flex items-center justify-center`}>
              <Icon className="w-5 h-5 text-white" />
            </div>
          </div>
          <div className="ml-4 flex-1">
            <p className="text-sm font-medium text-gray-500">{title}</p>
            <div className="flex items-center">
              <p className="text-2xl font-semibold text-gray-900">{value}</p>
              {trend && (
                <div className="ml-2 flex items-center text-sm text-green-600">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  {trend}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Overview of your projects and AI workflow status
          </p>
        </div>
        <div className="flex space-x-3">
          <Link to="/projects">
            <Button variant="secondary">
              <FolderOpen className="w-4 h-4 mr-2" />
              View All Projects
            </Button>
          </Link>
          <Button onClick={() => setShowCreateModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            New Project
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Projects"
          value={stats.projectsCount}
          icon={FolderOpen}
          color="bg-blue-500"
        />
        <StatCard
          title="AI Processing"
          value={stats.aiProcessing}
          icon={Bot}
          color="bg-purple-500"
        />
        <StatCard
          title="Completed Today"
          value={stats.completedToday}
          icon={CheckCircle}
          color="bg-green-500"
        />
        <StatCard
          title="In Acceptance"
          value={stats.inAcceptance}
          icon={Eye}
          color="bg-yellow-500"
        />
      </div>

      {/* Recent Projects */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Recent Projects</CardTitle>
            <Link 
              to="/projects" 
              className="text-blue-600 hover:text-blue-500 text-sm font-medium"
            >
              View all
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {recentProjects.length > 0 ? (
            <div className="space-y-4">
              {recentProjects.map((project) => (
                <div 
                  key={project.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FolderOpen className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900">
                        <Link 
                          to={`/projects/${project.id}`} 
                          className="hover:text-blue-600"
                        >
                          {project.name}
                        </Link>
                      </h3>
                      {project.description && (
                        <p className="text-sm text-gray-500 mt-1">
                          {project.description.length > 100 
                            ? `${project.description.substring(0, 100)}...` 
                            : project.description
                          }
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">
                      {formatDate(project.created_at)}
                    </span>
                    <Link to={`/projects/${project.id}`}>
                      <Button
                        variant="ghost"
                        size="sm"
                      >
                        <ArrowRight className="w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FolderOpen className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
              <p className="text-gray-500 mb-4">Get started by creating your first project</p>
              <Button onClick={() => setShowCreateModal(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Project
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* AI Workflow Status */}
        <Card>
          <CardHeader>
            <CardTitle>AI Workflow Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Ready for AI</span>
                <StatusBadge status="ready_for_ai" />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">In Progress</span>
                <StatusBadge status="in_progress" />
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">In Acceptance</span>
                <StatusBadge status="acceptance" />
              </div>
            </div>
            <div className="mt-4">
              <Link to="/workflow">
                <Button variant="secondary" className="w-full">
                  <Bot className="w-4 h-4 mr-2" />
                  View Workflow Details
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button
                variant="secondary"
                className="w-full justify-start"
                onClick={() => setShowCreateModal(true)}
              >
                <Plus className="w-4 h-4 mr-3" />
                Create New Project
              </Button>
              <Link to="/projects">
                <Button variant="secondary" className="w-full justify-start">
                  <FolderOpen className="w-4 h-4 mr-3" />
                  Browse Projects
                </Button>
              </Link>
              <Link to="/board">
                <Button variant="secondary" className="w-full justify-start">
                  <CheckCircle className="w-4 h-4 mr-3" />
                  View Board
                </Button>
              </Link>
              <Link to="/workflow">
                <Button variant="secondary" className="w-full justify-start">
                  <Bot className="w-4 h-4 mr-3" />
                  Monitor AI Workflows
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Create Project Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create New Project"
        size="md"
      >
        <ProjectForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setShowCreateModal(false)}
          isModal={true}
        />
      </Modal>
    </div>
  );
};

export default Dashboard;
