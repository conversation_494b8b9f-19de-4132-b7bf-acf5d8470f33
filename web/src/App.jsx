import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AppProvider } from './contexts/AppContext';
import { ErrorBoundary } from './components/ui';
import Layout from './components/layout/Layout';
import Dashboard from './pages/Dashboard';
import Projects from './pages/Projects';
import ProjectDetail from './pages/ProjectDetail';
import EpicDetail from './pages/EpicDetail';
import FeatureDetail from './pages/FeatureDetail';
import BacklogItemDetail from './pages/BacklogItemDetail';
import Board from './pages/Board';
import Workflow from './pages/Workflow';

function App() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/" element={<Layout />}>
                <Route index element={<Navigate to="/dashboard" replace />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="projects" element={<Projects />} />
                <Route path="projects/:id" element={<ProjectDetail />} />
                <Route path="epics/:id" element={<EpicDetail />} />
                <Route path="features/:id" element={<FeatureDetail />} />
                <Route path="backlog-items/:id" element={<BacklogItemDetail />} />
                <Route path="board" element={<Board />} />
                <Route path="workflow" element={<Workflow />} />
              </Route>
            </Routes>
          </div>
        </Router>
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App;
