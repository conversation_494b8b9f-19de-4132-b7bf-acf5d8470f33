{"name": "@dnd-kit/sortable", "version": "10.0.0", "description": "Official sortable preset and sensors for dnd kit", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/clauderic/dnd-kit.git", "directory": "packages/sortable"}, "scripts": {"start": "tsdx watch --tsconfig tsconfig.build.json --verbose --noClean", "build": "tsdx build --tsconfig tsconfig.build.json", "test": "tsdx test", "lint": "tsdx lint", "prepublish": "npm run build"}, "main": "dist/index.js", "module": "dist/sortable.esm.js", "typings": "dist/index.d.ts", "files": ["README.md", "CHANGELOG.md", "LICENSE", "dist"], "dependencies": {"@dnd-kit/utilities": "^3.2.2", "tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0", "@dnd-kit/core": "^6.3.0"}, "devDependencies": {"@dnd-kit/core": "^6.3.0"}, "publishConfig": {"access": "public"}}